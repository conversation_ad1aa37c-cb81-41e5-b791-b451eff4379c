import axios from 'axios'
import { Message, Loading } from 'element-ui'
import store from '@/store'
import router from '@/router'

// 获取环境配置
const config = window.getConfig
  ? window.getConfig()
  : {
    baseURL: '/api',
    timeout: 15000
  }

// 创建axios实例
const service = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// loading实例
let loadingInstance = null
let requestCount = 0

// 显示loading
function showLoading(config) {
  if (config.loading !== false) {
    requestCount++
    if (requestCount === 1) {
      loadingInstance = Loading.service({
        text: '加载中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
    }
  }
}

// 隐藏loading
function hideLoading() {
  requestCount--
  if (requestCount === 0 && loadingInstance) {
    loadingInstance.close()
    loadingInstance = null
  }
}

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 显示loading
    showLoading(config)

    // 添加token
    const token = store.getters.token || localStorage.getItem('token')
    if (token) {
      config.headers.Token =  token
    }

    // 添加时间戳防止缓存
    if (config.method === 'get') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }

    return config
  },
  error => {
    hideLoading()
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    hideLoading()

    const res = response.data
    // 根据后端返回的状态码进行处理
    if (res.Ret !== 1) {
      // 处理业务错误
      Message({
        message: res.Msg || '请求失败',
        type: 'error',
        duration: 3000
      })

      // 401: 未授权，跳转到登录页
      if (res.Ret === 401) {
        store.dispatch('user/logout').then(() => {
          router.push('/login')
        })
      }

      return Promise.reject(new Error(res.Msg || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    hideLoading()

    let message = '网络错误'

    if (error.response) {
      const { status, data } = error.response

      switch (status) {
      case 400:
        message = data.Msg || '请求参数错误'
        break
      case 401:
        message = '未授权，请重新登录'
        break
      case 403:
        message = '拒绝访问'
        break
      case 404:
        message = '请求地址不存在'
        break
      case 500:
        message = '服务器内部错误'
        break
      case 502:
        message = '网关错误'
        break
      case 503:
        message = '服务不可用'
        break
      case 504:
        message = '网关超时'
        break
      default:
        message = data.Msg || `连接错误${status}`
      }
    } else if (error.code === 'ECONNABORTED') {
      message = '请求超时'
    } else if (error.message.includes('Network Error')) {
      message = '网络连接异常'
    }

    Message({
      message,
      type: 'error',
      duration: 3000
    })

    return Promise.reject(error)
  }
)

// 封装请求方法
const request = {
  // GET请求
  get(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      ...config
    })
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return service({
      method: 'post',
      url,
      data,
      ...config
    })
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return service({
      method: 'put',
      url,
      data,
      ...config
    })
  },

  // DELETE请求
  delete(url, params = {}, config = {}) {
    return service({
      method: 'delete',
      url,
      params,
      ...config
    })
  },

  // PATCH请求
  patch(url, data = {}, config = {}) {
    return service({
      method: 'patch',
      url,
      data,
      ...config
    })
  },

  // 文件上传
  upload(url, formData, config = {}) {
    return service({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },

  // 文件下载
  download(url, params = {}, config = {}) {
    return service({
      method: 'get',
      url,
      params,
      responseType: 'blob',
      ...config
    })
  }
}

export default request
export { service }
